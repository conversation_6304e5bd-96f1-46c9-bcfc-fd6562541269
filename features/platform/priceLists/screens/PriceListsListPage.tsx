import { getListByPath } from "@/features/dashboard/actions";
import { getFilteredPriceLists, getPriceListStatusCounts } from "../actions";
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Triangle, Square, Circle } from "lucide-react";
import { PriceListDetailsComponent } from "../components/PriceListDetailsComponent";
import { StatusTabs } from "../components/StatusTabs";
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import type { SortOption } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from "@/features/dashboard/components/PaginationWrapper";

interface PriceListsListPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function PriceListsListPage({ searchParams }: PriceListsListPageProps) {
  const resolvedSearchParams = await searchParams;

  // Extract search parameters
  const search = typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : '';
  const status = typeof resolvedSearchParams.status === 'string' ?
                resolvedSearchParams.status as 'active' | 'draft' : undefined;
  const type = typeof resolvedSearchParams.type === 'string' ?
              resolvedSearchParams.type as 'sale' | 'override' : undefined;
  const page = parseInt(typeof resolvedSearchParams.page === 'string' ? resolvedSearchParams.page : '1');
  const pageSize = parseInt(typeof resolvedSearchParams.pageSize === 'string' ? resolvedSearchParams.pageSize : '20');
  const sortBy = typeof resolvedSearchParams.sortBy === 'string' ? resolvedSearchParams.sortBy : 'createdAt';
  const sortOrder = (typeof resolvedSearchParams.sortOrder === 'string' ? resolvedSearchParams.sortOrder : 'desc') as 'asc' | 'desc';

  // Fetch data
  const [priceListsResponse, statusCountsResponse, listResponse] = await Promise.all([
    getFilteredPriceLists({
      search,
      status,
      type,
      page,
      pageSize,
      sortBy,
      sortOrder
    }),
    getPriceListStatusCounts(),
    getListByPath('price-lists')
  ]);

  if (!priceListsResponse.success || !statusCountsResponse.success || !listResponse.success) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-50">
            Error loading price lists
          </h3>
          <p className="text-muted-foreground">
            {priceListsResponse.error || statusCountsResponse.error || listResponse.error}
          </p>
        </div>
      </div>
    );
  }

  const { priceLists, totalCount, totalPages } = priceListsResponse.data;
  const statusCounts = statusCountsResponse.data;
  const list = listResponse.data;

  // Sort options for the filter bar
  const sortOptions: SortOption[] = [
    { value: 'createdAt', label: 'Created Date' },
    { value: 'name', label: 'Name' },
    { value: 'type', label: 'Type' },
    { value: 'status', label: 'Status' },
  ];

  const sort = sortOptions.find(option => option.value === sortBy) || sortOptions[0];

  return (
    <div className="flex flex-col h-full">
      <PageBreadcrumbs
        items={[
          { type: 'link', label: 'Dashboard', href: '/' },
          { type: 'page', label: 'Platform', showModelSwitcher: true, switcherType: 'platform' },
          { type: 'page', label: 'Price Lists' },
        ]}
      />

      <div className="flex flex-col flex-1 min-h-0">
        <div className="border-gray-200 dark:border-gray-800">
          <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
              Price Lists
            </h1>
            <p className="text-muted-foreground">
              <span>Manage your pricing strategies and customer group pricing.</span>
            </p>
          </div>
        </div>

        <PlatformFilterBar
          list={{
            key: list.key,
            path: list.path,
            label: list.label,
            singular: list.singular,
            plural: list.plural,
            description: list.description || undefined,
            labelField: list.labelField as string,
            initialColumns: list.initialColumns,
            groups: list.groups as unknown as string[],
            graphql: {
              plural: list.plural,
              singular: list.singular
            },
            fields: list.fields
          }}
          currentSort={sort}
        />

        {/* Status Tabs */}
        <StatusTabs statusCounts={statusCounts} />

        {/* Additional Summary Cards */}
        <div className="px-4 md:px-6 mb-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Sale Lists</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-50 mt-1">
                {statusCounts.sale}
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">Override Lists</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-50 mt-1">
                {statusCounts.override}
              </p>
            </div>
          </div>
        </div>

        {/* Price Lists List */}
        <div className="flex-1 px-4 md:px-6">
          {priceLists.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-50">
                  No price lists found
                </h3>
                <p className="text-muted-foreground">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {priceLists.map((priceList) => (
                <PriceListDetailsComponent key={priceList.id} priceList={priceList} />
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-4 md:px-6 py-4 border-t">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              totalItems={totalCount}
              itemsPerPage={pageSize}
            />
          </div>
        )}
      </div>
    </div>
  );
}
