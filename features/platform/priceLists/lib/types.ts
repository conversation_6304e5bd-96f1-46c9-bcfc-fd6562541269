// Price List-related types for the platform

export interface PriceListCustomerGroup {
  id: string;
  name: string;
  usersCount?: number;
}

export interface PriceListMoneyAmount {
  id: string;
  amount: number;
  currencyCode: string;
  productVariant?: {
    id: string;
    title: string;
    product: {
      id: string;
      title: string;
    };
  };
}

export interface PriceList {
  id: string;
  name: string;
  description: string;
  type: 'sale' | 'override';
  status: 'active' | 'draft';
  startsAt?: string;
  endsAt?: string;
  customerGroups: PriceListCustomerGroup[];
  moneyAmounts: PriceListMoneyAmount[];
  moneyAmountsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface PriceListListResponse {
  priceLists: PriceList[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface PriceListStatusCounts {
  all: number;
  active: number;
  draft: number;
  sale: number;
  override: number;
}

export interface PriceListFilters {
  search?: string;
  status?: 'active' | 'draft';
  type?: 'sale' | 'override';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export type PriceListStatus = 'active' | 'draft';
export type PriceListType = 'sale' | 'override';
