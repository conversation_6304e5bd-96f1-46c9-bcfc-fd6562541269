import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { MoreHorizontal, Calendar, Users, DollarSign, Tag } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import type { PriceList } from "../lib/types";

interface PriceListDetailsComponentProps {
  priceList: PriceList;
}

export function PriceListDetailsComponent({ priceList }: PriceListDetailsComponentProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'emerald';
      case 'draft':
        return 'yellow';
      default:
        return 'gray';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sale':
        return 'blue';
      case 'override':
        return 'purple';
      default:
        return 'gray';
    }
  };

  const isActive = () => {
    const now = new Date();
    const startsAt = priceList.startsAt ? new Date(priceList.startsAt) : null;
    const endsAt = priceList.endsAt ? new Date(priceList.endsAt) : null;

    if (priceList.status !== 'active') return false;
    if (startsAt && startsAt > now) return false;
    if (endsAt && endsAt < now) return false;
    return true;
  };

  const getDateRange = () => {
    if (!priceList.startsAt && !priceList.endsAt) return 'No date restrictions';
    
    const startsAt = priceList.startsAt ? new Date(priceList.startsAt).toLocaleDateString() : 'No start date';
    const endsAt = priceList.endsAt ? new Date(priceList.endsAt).toLocaleDateString() : 'No end date';
    
    if (priceList.startsAt && priceList.endsAt) {
      return `${startsAt} - ${endsAt}`;
    } else if (priceList.startsAt) {
      return `From ${startsAt}`;
    } else if (priceList.endsAt) {
      return `Until ${endsAt}`;
    }
    
    return 'No date restrictions';
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <Link 
                  href={`/platform/price-lists/${priceList.id}`}
                  className="text-lg font-semibold text-gray-900 dark:text-gray-50 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  {priceList.name}
                </Link>
                <Badge variant="outline" className={`bg-${getStatusColor(priceList.status)}-50 text-${getStatusColor(priceList.status)}-700 border-${getStatusColor(priceList.status)}-200`}>
                  {priceList.status.charAt(0).toUpperCase() + priceList.status.slice(1)}
                </Badge>
                <Badge variant="outline" className={`bg-${getTypeColor(priceList.type)}-50 text-${getTypeColor(priceList.type)}-700 border-${getTypeColor(priceList.type)}-200`}>
                  {priceList.type.charAt(0).toUpperCase() + priceList.type.slice(1)}
                </Badge>
                {isActive() && (
                  <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                    Currently Active
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {priceList.description}
              </p>
              <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <DollarSign className="h-4 w-4" />
                  <span>{priceList.moneyAmountsCount} prices</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4" />
                  <span>{priceList.customerGroups.length} customer groups</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{getDateRange()}</span>
                </div>
              </div>
              {priceList.customerGroups.length > 0 && (
                <div className="mt-2">
                  <span className="text-sm text-muted-foreground">Customer Groups: </span>
                  {priceList.customerGroups.map((group, index) => (
                    <Badge key={group.id} variant="outline" size="sm" className="ml-1">
                      {group.name}
                      {group.usersCount && ` (${group.usersCount} users)`}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/platform/price-lists/${priceList.id}`}>
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/platform/price-lists/${priceList.id}/edit`}>
                  Edit Price List
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                {priceList.status === 'active' ? 'Set to Draft' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete Price List
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      {/* Sample Prices */}
      {priceList.moneyAmounts.length > 0 && (
        <CardContent className="pt-0">
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-50 mb-2">
              Sample Prices
            </h4>
            <div className="space-y-2">
              {priceList.moneyAmounts.slice(0, 3).map((moneyAmount) => (
                <div key={moneyAmount.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    {moneyAmount.productVariant && (
                      <Link 
                        href={`/platform/products/${moneyAmount.productVariant.product.id}`}
                        className="font-medium text-blue-600 hover:text-blue-800"
                      >
                        {moneyAmount.productVariant.product.title}
                      </Link>
                    )}
                    {moneyAmount.productVariant?.title && (
                      <Badge variant="outline" size="sm">
                        {moneyAmount.productVariant.title}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <span className="font-medium">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: moneyAmount.currencyCode.toUpperCase(),
                      }).format(moneyAmount.amount / 100)}
                    </span>
                  </div>
                </div>
              ))}
              {priceList.moneyAmounts.length > 3 && (
                <div className="text-sm text-muted-foreground">
                  +{priceList.moneyAmounts.length - 3} more prices
                </div>
              )}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
