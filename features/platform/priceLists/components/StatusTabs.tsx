"use client";

import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Circle } from "lucide-react";

interface StatusTabsProps {
  statusCounts: {
    all: number;
    active: number;
    draft: number;
  };
}

export function StatusTabs({ statusCounts }: StatusTabsProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const currentStatus = searchParams?.get('status') || 'all';

  const handleStatusChange = (status: string) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    
    if (status === 'all') {
      params.delete('status');
    } else {
      params.set('status', status);
    }
    
    // Reset to first page when changing status
    params.set('page', '1');
    
    const queryString = params.toString();
    router.push(`${pathname}${queryString ? `?${queryString}` : ''}`);
  };

  const tabs = [
    {
      value: 'all',
      label: 'All',
      count: statusCounts.all,
      color: 'blue'
    },
    {
      value: 'active',
      label: 'Active',
      count: statusCounts.active,
      color: 'emerald'
    },
    {
      value: 'draft',
      label: 'Draft',
      count: statusCounts.draft,
      color: 'yellow'
    }
  ];

  return (
    <div className="px-4 md:px-6 mb-6">
      <Tabs value={currentStatus} onValueChange={handleStatusChange}>
        <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className="data-[state=active]:bg-muted data-[state=active]:after:bg-primary relative overflow-hidden rounded-none border py-2 after:pointer-events-none after:absolute after:inset-x-0 after:bottom-0 after:h-0.5 first:rounded-s last:rounded-e"
            >
              <div className="flex items-center space-x-2">
                <Circle className={`h-3 w-3 text-${tab.color}-500`} />
                <span>{tab.label}</span>
                <span className="text-xs text-muted-foreground">
                  ({tab.count})
                </span>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
}
