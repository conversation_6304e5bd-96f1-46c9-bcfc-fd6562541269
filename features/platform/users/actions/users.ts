'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';

// Types for User data
export interface UserOrder {
  id: string;
  displayId: number;
  status: string;
  total: string;
  createdAt: string;
}

export interface UserWithRelations {
  id: string;
  name: string;
  email: string;
  phone: string;
  hasAccount: boolean;
  onboardingStatus: string;
  role?: {
    id: string;
    name: string;
  };
  team?: {
    id: string;
    name: string;
  };
  orders: UserOrder[];
  ordersCount: number;
  addresses: Array<{
    id: string;
    firstName: string;
    lastName: string;
    city: string;
    countryCode: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

// GraphQL Queries
const GET_FILTERED_USERS_QUERY = `
  query GetFilteredUsers(
    $where: UserWhereInput
    $orderBy: [UserOrderByInput!]
    $take: Int
    $skip: Int
  ) {
    users(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      name
      email
      phone
      hasAccount
      onboardingStatus
      role {
        id
        name
      }
      team {
        id
        name
      }
      orders(take: 5, orderBy: { createdAt: desc }) {
        id
        displayId
        status
        total
        createdAt
      }
      ordersCount
      addresses(take: 3) {
        id
        firstName
        lastName
        city
        countryCode
      }
      createdAt
      updatedAt
    }
    usersCount(where: $where)
  }
`;

const GET_USER_QUERY = `
  query GetUser($id: ID!) {
    user(where: { id: $id }) {
      id
      name
      email
      phone
      hasAccount
      onboardingStatus
      role {
        id
        name
      }
      team {
        id
        name
      }
      orders(orderBy: { createdAt: desc }) {
        id
        displayId
        status
        total
        createdAt
      }
      ordersCount
      addresses {
        id
        firstName
        lastName
        address1
        address2
        city
        province
        postalCode
        countryCode
        phone
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_USER_STATUS_COUNTS_QUERY = `
  query GetUserStatusCounts {
    allUsersCount: usersCount
    hasAccountCount: usersCount(where: { hasAccount: { equals: true } })
    noAccountCount: usersCount(where: { hasAccount: { equals: false } })
    onboardingCompletedCount: usersCount(where: { onboardingStatus: { equals: "completed" } })
    onboardingInProgressCount: usersCount(where: { onboardingStatus: { equals: "in_progress" } })
  }
`;

// Server Actions
export async function getFilteredUsers({
  search = '',
  hasAccount,
  onboardingStatus,
  page = 1,
  pageSize = 20,
  sortBy = 'createdAt',
  sortOrder = 'desc'
}: {
  search?: string;
  hasAccount?: boolean;
  onboardingStatus?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  try {
    const skip = (page - 1) * pageSize;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (hasAccount !== undefined) {
      where.hasAccount = { equals: hasAccount };
    }

    if (onboardingStatus) {
      where.onboardingStatus = { equals: onboardingStatus };
    }

    const orderBy = [{ [sortBy]: sortOrder }];

    const response = await keystoneClient(GET_FILTERED_USERS_QUERY, {
      where,
      orderBy,
      take: pageSize,
      skip
    });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: {
        users: response.data.users as UserWithRelations[],
        totalCount: response.data.usersCount,
        page,
        pageSize,
        totalPages: Math.ceil(response.data.usersCount / pageSize)
      }
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    return {
      success: false,
      error: 'Failed to fetch users'
    };
  }
}

export async function getUser(id: string) {
  try {
    const response = await keystoneClient(GET_USER_QUERY, { id });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: {
        user: response.data.user as UserWithRelations
      }
    };
  } catch (error) {
    console.error('Error fetching user:', error);
    return {
      success: false,
      error: 'Failed to fetch user'
    };
  }
}

export async function getUserStatusCounts() {
  try {
    const response = await keystoneClient(GET_USER_STATUS_COUNTS_QUERY);

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: {
        all: response.data.allUsersCount,
        hasAccount: response.data.hasAccountCount,
        noAccount: response.data.noAccountCount,
        onboardingCompleted: response.data.onboardingCompletedCount,
        onboardingInProgress: response.data.onboardingInProgressCount
      }
    };
  } catch (error) {
    console.error('Error fetching user status counts:', error);
    return {
      success: false,
      error: 'Failed to fetch user status counts'
    };
  }
}

export async function updateUser(id: string, data: Partial<UserWithRelations>) {
  try {
    const UPDATE_USER_QUERY = `
      mutation UpdateUser($id: ID!, $data: UserUpdateInput!) {
        updateUser(where: { id: $id }, data: $data) {
          id
          name
          email
          phone
          hasAccount
          onboardingStatus
        }
      }
    `;

    const response = await keystoneClient(UPDATE_USER_QUERY, { id, data });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: response.data.updateUser
    };
  } catch (error) {
    console.error('Error updating user:', error);
    return {
      success: false,
      error: 'Failed to update user'
    };
  }
}

export async function deleteUser(id: string) {
  try {
    const DELETE_USER_QUERY = `
      mutation DeleteUser($id: ID!) {
        deleteUser(where: { id: $id }) {
          id
        }
      }
    `;

    const response = await keystoneClient(DELETE_USER_QUERY, { id });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting user:', error);
    return {
      success: false,
      error: 'Failed to delete user'
    };
  }
}
