import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { MoreHorizontal, Mail, Phone, MapPin, ShoppingBag } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import type { User } from "../lib/types";

interface UserDetailsComponentProps {
  user: User;
}

export function UserDetailsComponent({ user }: UserDetailsComponentProps) {
  const getOnboardingStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'emerald';
      case 'in_progress':
        return 'blue';
      case 'dismissed':
        return 'gray';
      default:
        return 'yellow';
    }
  };

  const getAccountStatusColor = (hasAccount: boolean) => {
    return hasAccount ? 'emerald' : 'gray';
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <Link
                  href={`/platform/users/${user.id}`}
                  className="text-lg font-semibold text-gray-900 dark:text-gray-50 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  {user.name}
                </Link>
                <Badge variant="outline" className={`bg-${getAccountStatusColor(user.hasAccount)}-50 text-${getAccountStatusColor(user.hasAccount)}-700 border-${getAccountStatusColor(user.hasAccount)}-200`}>
                  {user.hasAccount ? 'Has Account' : 'No Account'}
                </Badge>
                <Badge variant="outline" className={`bg-${getOnboardingStatusColor(user.onboardingStatus)}-50 text-${getOnboardingStatusColor(user.onboardingStatus)}-700 border-${getOnboardingStatusColor(user.onboardingStatus)}-200`}>
                  {user.onboardingStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
              </div>
              <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <Mail className="h-4 w-4" />
                  <span>{user.email}</span>
                </div>
                {user.phone && (
                  <div className="flex items-center space-x-1">
                    <Phone className="h-4 w-4" />
                    <span>{user.phone}</span>
                  </div>
                )}
                <div className="flex items-center space-x-1">
                  <ShoppingBag className="h-4 w-4" />
                  <span>{user.orders.length} orders</span>
                </div>
                {user.addresses.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span>{user.addresses.length} addresses</span>
                  </div>
                )}
              </div>
              {user.role && (
                <div className="mt-2">
                  <Badge variant="secondary">
                    {user.role.name}
                  </Badge>
                  {user.team && (
                    <Badge variant="outline" className="ml-2">
                      Team: {user.team.name}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/platform/users/${user.id}`}>
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/platform/users/${user.id}/edit`}>
                  Edit User
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete User
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      {user.orders.length > 0 && (
        <CardContent className="pt-0">
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-50 mb-2">
              Recent Orders
            </h4>
            <div className="space-y-2">
              {user.orders.slice(0, 3).map((order) => (
                <div key={order.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <Link
                      href={`/platform/orders/${order.id}`}
                      className="font-medium text-blue-600 hover:text-blue-800"
                    >
                      #{order.displayId}
                    </Link>
                    <Badge variant="outline" size="sm">
                      {order.status}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <span>{order.total}</span>
                    <span>•</span>
                    <span>{new Date(order.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
              {user.orders.length > 3 && (
                <div className="text-sm text-muted-foreground">
                  +{user.orders.length - 3} more orders
                </div>
              )}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
