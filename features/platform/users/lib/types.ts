// User-related types for the platform

export interface UserOrder {
  id: string;
  displayId: number;
  status: string;
  total: string;
  createdAt: string;
}

export interface UserAddress {
  id: string;
  firstName: string;
  lastName: string;
  address1: string;
  address2?: string;
  city: string;
  province?: string;
  postalCode: string;
  countryCode: string;
  phone?: string;
}

export interface UserRole {
  id: string;
  name: string;
}

export interface UserTeam {
  id: string;
  name: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  hasAccount: boolean;
  onboardingStatus: 'not_started' | 'in_progress' | 'completed' | 'dismissed';
  role?: UserRole;
  team?: UserTeam;
  orders: UserOrder[];
  ordersCount: number;
  addresses: UserAddress[];
  createdAt: string;
  updatedAt: string;
}

export interface UserListResponse {
  users: User[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface UserStatusCounts {
  all: number;
  hasAccount: number;
  noAccount: number;
  onboardingCompleted: number;
  onboardingInProgress: number;
}

export interface UserFilters {
  search?: string;
  hasAccount?: boolean;
  onboardingStatus?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
