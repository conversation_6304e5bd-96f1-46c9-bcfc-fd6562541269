import { notFound } from 'next/navigation';
import { getUser } from '../actions';
import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { UserPageClient } from './UserPageClient';

interface PageProps {
  params: Promise<{ id: string }>;
}

export async function UserDetailPage({ params }: PageProps) {
  const resolvedParams = await params;
  const response = await getUser(resolvedParams.id);

  const user = response?.data?.user;

  if (!user) {
    notFound();
  }

  // Extract UI configuration
  const uiConfig = {
    hideDelete: false,
  };

  return (
    <>
      <PageBreadcrumbs
        items={[
          { type: 'link', label: 'Dashboard', href: '/' },
          { type: 'page', label: 'Platform', showModelSwitcher: true, switcherType: 'platform' },
          { type: 'link', label: 'Users', href: '/platform/users' },
          { type: 'page', label: user.name || user.email },
        ]}
      />
      <UserPageClient
        user={user}
        id={resolvedParams.id}
        listKey="users"
        fieldModes={{}}
        fieldPositions={{}}
        uiConfig={uiConfig}
      />
    </>
  );
}
