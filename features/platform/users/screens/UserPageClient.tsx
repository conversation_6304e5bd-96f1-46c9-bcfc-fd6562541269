"use client";

import { useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Mail, Phone, MapPin, ShoppingBag, User as UserIcon, Settings } from "lucide-react";
import Link from "next/link";
import type { User } from "../lib/types";

interface UserPageClientProps {
  user: User;
  id: string;
  listKey: string;
  fieldModes: Record<string, any>;
  fieldPositions: Record<string, any>;
  uiConfig: {
    hideDelete: boolean;
  };
}

export function UserPageClient({ 
  user, 
  id, 
  listKey, 
  fieldModes, 
  fieldPositions, 
  uiConfig 
}: UserPageClientProps) {
  const [activeTab, setActiveTab] = useState("overview");

  const getOnboardingStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'emerald';
      case 'in_progress':
        return 'blue';
      case 'dismissed':
        return 'gray';
      default:
        return 'yellow';
    }
  };

  const getAccountStatusColor = (hasAccount: boolean) => {
    return hasAccount ? 'emerald' : 'gray';
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="border-b bg-white dark:bg-gray-900">
        <div className="px-4 md:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                  <UserIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
                  {user.name}
                </h1>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant="outline" className={`bg-${getAccountStatusColor(user.hasAccount)}-50 text-${getAccountStatusColor(user.hasAccount)}-700 border-${getAccountStatusColor(user.hasAccount)}-200`}>
                    {user.hasAccount ? 'Has Account' : 'No Account'}
                  </Badge>
                  <Badge variant="outline" className={`bg-${getOnboardingStatusColor(user.onboardingStatus)}-50 text-${getOnboardingStatusColor(user.onboardingStatus)}-700 border-${getOnboardingStatusColor(user.onboardingStatus)}-200`}>
                    {user.onboardingStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Badge>
                  {user.role && (
                    <Badge variant="secondary">
                      {user.role.name}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Edit User
              </Button>
              {!uiConfig.hideDelete && (
                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                  Delete
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 md:p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="orders">Orders ({user.ordersCount})</TabsTrigger>
            <TabsTrigger value="addresses">Addresses ({user.addresses.length})</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Mail className="w-5 h-5" />
                    <span>Contact Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Email</label>
                    <p className="text-sm">{user.email}</p>
                  </div>
                  {user.phone && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Phone</label>
                      <p className="text-sm">{user.phone}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Account Status</label>
                    <p className="text-sm">{user.hasAccount ? 'Has Account' : 'No Account'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Onboarding Status</label>
                    <p className="text-sm">{user.onboardingStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Organization */}
              <Card>
                <CardHeader>
                  <CardTitle>Organization</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {user.role && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Role</label>
                      <p className="text-sm">{user.role.name}</p>
                    </div>
                  )}
                  {user.team && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Team</label>
                      <p className="text-sm">{user.team.name}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Member Since</label>
                    <p className="text-sm">{new Date(user.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                    <p className="text-sm">{new Date(user.updatedAt).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <ShoppingBag className="w-5 h-5 text-blue-500" />
                    <span className="text-sm font-medium">Total Orders</span>
                  </div>
                  <p className="text-2xl font-bold mt-2">{user.ordersCount}</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-5 h-5 text-green-500" />
                    <span className="text-sm font-medium">Addresses</span>
                  </div>
                  <p className="text-2xl font-bold mt-2">{user.addresses.length}</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <UserIcon className="w-5 h-5 text-purple-500" />
                    <span className="text-sm font-medium">Account Type</span>
                  </div>
                  <p className="text-lg font-semibold mt-2">{user.hasAccount ? 'Registered' : 'Guest'}</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="orders" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Order History</CardTitle>
              </CardHeader>
              <CardContent>
                {user.orders.length === 0 ? (
                  <p className="text-muted-foreground">No orders found.</p>
                ) : (
                  <div className="space-y-4">
                    {user.orders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <Link 
                            href={`/platform/orders/${order.id}`}
                            className="font-medium text-blue-600 hover:text-blue-800"
                          >
                            #{order.displayId}
                          </Link>
                          <Badge variant="outline">
                            {order.status}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>{order.total}</span>
                          <span>{new Date(order.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="addresses" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Saved Addresses</CardTitle>
              </CardHeader>
              <CardContent>
                {user.addresses.length === 0 ? (
                  <p className="text-muted-foreground">No addresses found.</p>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {user.addresses.map((address) => (
                      <div key={address.id} className="p-4 border rounded-lg">
                        <div className="font-medium">
                          {address.firstName} {address.lastName}
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          <p>{address.address1}</p>
                          {address.address2 && <p>{address.address2}</p>}
                          <p>{address.city}, {address.province} {address.postalCode}</p>
                          <p>{address.countryCode}</p>
                          {address.phone && <p>Phone: {address.phone}</p>}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Activity tracking coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
