// Discount-related types for the platform

export interface DiscountRule {
  id: string;
  type: 'fixed' | 'percentage' | 'free_shipping';
  value: number;
  allocation?: 'total' | 'item';
  description?: string;
}

export interface DiscountUsage {
  id: string;
  displayId: number;
  total: string;
  createdAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface DiscountRegion {
  id: string;
  name: string;
  currencyCode: string;
}

export interface Discount {
  id: string;
  code: string;
  isDynamic: boolean;
  isDisabled: boolean;
  stackable: boolean;
  startsAt: string;
  endsAt?: string;
  usageLimit?: number;
  usageCount: number;
  validDuration?: string;
  discountRule?: DiscountRule;
  orders: DiscountUsage[];
  regions: DiscountRegion[];
  createdAt: string;
  updatedAt: string;
}

export interface DiscountListResponse {
  discounts: Discount[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface DiscountStatusCounts {
  all: number;
  active: number;
  disabled: number;
  expired: number;
  scheduled: number;
}

export interface DiscountFilters {
  search?: string;
  isDisabled?: boolean;
  status?: 'active' | 'disabled' | 'expired' | 'scheduled';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export type DiscountStatus = 'active' | 'disabled' | 'expired' | 'scheduled';
