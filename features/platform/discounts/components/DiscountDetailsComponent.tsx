import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { MoreHorizontal, Calendar, Users, Percent, DollarSign, Truck } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import type { Discount } from "../lib/types";

interface DiscountDetailsComponentProps {
  discount: Discount;
}

export function DiscountDetailsComponent({ discount }: DiscountDetailsComponentProps) {
  const getDiscountStatus = () => {
    const now = new Date();
    const startsAt = new Date(discount.startsAt);
    const endsAt = discount.endsAt ? new Date(discount.endsAt) : null;

    if (discount.isDisabled) return { status: 'disabled', color: 'gray' };
    if (startsAt > now) return { status: 'scheduled', color: 'blue' };
    if (endsAt && endsAt < now) return { status: 'expired', color: 'red' };
    return { status: 'active', color: 'emerald' };
  };

  const { status, color } = getDiscountStatus();

  const getDiscountTypeIcon = () => {
    if (!discount.discountRule) return <Percent className="h-4 w-4" />;
    
    switch (discount.discountRule.type) {
      case 'fixed':
        return <DollarSign className="h-4 w-4" />;
      case 'percentage':
        return <Percent className="h-4 w-4" />;
      case 'free_shipping':
        return <Truck className="h-4 w-4" />;
      default:
        return <Percent className="h-4 w-4" />;
    }
  };

  const getDiscountValue = () => {
    if (!discount.discountRule) return 'No rule';
    
    switch (discount.discountRule.type) {
      case 'fixed':
        return `$${(discount.discountRule.value / 100).toFixed(2)}`;
      case 'percentage':
        return `${discount.discountRule.value}%`;
      case 'free_shipping':
        return 'Free Shipping';
      default:
        return discount.discountRule.value.toString();
    }
  };

  const getUsagePercentage = () => {
    if (!discount.usageLimit) return 0;
    return Math.min((discount.usageCount / discount.usageLimit) * 100, 100);
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <Link 
                  href={`/platform/discounts/${discount.id}`}
                  className="text-lg font-semibold text-gray-900 dark:text-gray-50 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  {discount.code}
                </Link>
                <Badge variant="outline" className={`bg-${color}-50 text-${color}-700 border-${color}-200`}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Badge>
                {discount.stackable && (
                  <Badge variant="secondary" size="sm">
                    Stackable
                  </Badge>
                )}
                {discount.isDynamic && (
                  <Badge variant="outline" size="sm">
                    Dynamic
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  {getDiscountTypeIcon()}
                  <span>{getDiscountValue()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4" />
                  <span>{discount.usageCount} uses</span>
                  {discount.usageLimit && (
                    <span>/ {discount.usageLimit} limit</span>
                  )}
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {new Date(discount.startsAt).toLocaleDateString()}
                    {discount.endsAt && ` - ${new Date(discount.endsAt).toLocaleDateString()}`}
                  </span>
                </div>
              </div>
              {discount.discountRule?.description && (
                <p className="text-sm text-muted-foreground mt-2">
                  {discount.discountRule.description}
                </p>
              )}
              {discount.regions.length > 0 && (
                <div className="mt-2">
                  <span className="text-sm text-muted-foreground">Regions: </span>
                  {discount.regions.map((region, index) => (
                    <Badge key={region.id} variant="outline" size="sm" className="ml-1">
                      {region.name} ({region.currencyCode.toUpperCase()})
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/platform/discounts/${discount.id}`}>
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/platform/discounts/${discount.id}/edit`}>
                  Edit Discount
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                {discount.isDisabled ? 'Enable' : 'Disable'}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete Discount
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      {/* Usage Progress Bar */}
      {discount.usageLimit && (
        <CardContent className="pt-0">
          <div className="border-t pt-3">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-muted-foreground">Usage</span>
              <span className="font-medium">
                {discount.usageCount} / {discount.usageLimit}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`bg-${color}-500 h-2 rounded-full transition-all duration-300`}
                style={{ width: `${getUsagePercentage()}%` }}
              />
            </div>
          </div>
        </CardContent>
      )}

      {/* Recent Usage */}
      {discount.orders.length > 0 && (
        <CardContent className="pt-0">
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-50 mb-2">
              Recent Usage
            </h4>
            <div className="space-y-2">
              {discount.orders.slice(0, 3).map((order) => (
                <div key={order.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <Link 
                      href={`/platform/orders/${order.id}`}
                      className="font-medium text-blue-600 hover:text-blue-800"
                    >
                      #{order.displayId}
                    </Link>
                    {order.user && (
                      <span className="text-muted-foreground">
                        by {order.user.name}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-muted-foreground">
                    <span>{order.total}</span>
                    <span>•</span>
                    <span>{new Date(order.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
