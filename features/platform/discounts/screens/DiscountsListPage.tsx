import { getListByPath } from "@/features/dashboard/actions";
import { getFilteredDiscounts, getDiscountStatusCounts } from "../actions";
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Triangle, Square, Circle } from "lucide-react";
import { DiscountDetailsComponent } from "../components/DiscountDetailsComponent";
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import type { SortOption } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from "@/features/dashboard/components/PaginationWrapper";

interface DiscountsListPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function DiscountsListPage({ searchParams }: DiscountsListPageProps) {
  const resolvedSearchParams = await searchParams;

  // Extract search parameters
  const search = typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : '';
  const isDisabled = resolvedSearchParams.isDisabled === 'true' ? true :
                    resolvedSearchParams.isDisabled === 'false' ? false : undefined;
  const status = typeof resolvedSearchParams.status === 'string' ?
                resolvedSearchParams.status as 'active' | 'disabled' | 'expired' | 'scheduled' : undefined;
  const page = parseInt(typeof resolvedSearchParams.page === 'string' ? resolvedSearchParams.page : '1');
  const pageSize = parseInt(typeof resolvedSearchParams.pageSize === 'string' ? resolvedSearchParams.pageSize : '20');
  const sortBy = typeof resolvedSearchParams.sortBy === 'string' ? resolvedSearchParams.sortBy : 'createdAt';
  const sortOrder = (typeof resolvedSearchParams.sortOrder === 'string' ? resolvedSearchParams.sortOrder : 'desc') as 'asc' | 'desc';

  // Fetch data
  const [discountsResponse, statusCountsResponse, listResponse] = await Promise.all([
    getFilteredDiscounts({
      search,
      isDisabled,
      status,
      page,
      pageSize,
      sortBy,
      sortOrder
    }),
    getDiscountStatusCounts(),
    getListByPath('discounts')
  ]);

  if (!discountsResponse.success || !statusCountsResponse.success || !listResponse.success) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-50">
            Error loading discounts
          </h3>
          <p className="text-muted-foreground">
            {discountsResponse.error || statusCountsResponse.error || listResponse.error}
          </p>
        </div>
      </div>
    );
  }

  const { discounts, totalCount, totalPages } = discountsResponse.data;
  const statusCounts = statusCountsResponse.data;
  const list = listResponse.data;

  // Sort options for the filter bar
  const sortOptions: SortOption[] = [
    { value: 'createdAt', label: 'Created Date' },
    { value: 'code', label: 'Code' },
    { value: 'usageCount', label: 'Usage Count' },
    { value: 'startsAt', label: 'Start Date' },
  ];

  const sort = sortOptions.find(option => option.value === sortBy) || sortOptions[0];

  return (
    <div className="flex flex-col h-full">
      <PageBreadcrumbs
        items={[
          { type: 'link', label: 'Dashboard', href: '/' },
          { type: 'page', label: 'Platform', showModelSwitcher: true, switcherType: 'platform' },
          { type: 'page', label: 'Discounts' },
        ]}
      />

      <div className="flex flex-col flex-1 min-h-0">
        <div className="border-gray-200 dark:border-gray-800">
          <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
              Discounts
            </h1>
            <p className="text-muted-foreground">
              <span>Manage your discount codes and promotional offers.</span>
            </p>
          </div>
        </div>

        <PlatformFilterBar
          list={{
            key: list.key,
            path: list.path,
            label: list.label,
            singular: list.singular,
            plural: list.plural,
            description: list.description || undefined,
            labelField: list.labelField as string,
            initialColumns: list.initialColumns,
            groups: list.groups as unknown as string[],
            graphql: {
              plural: list.plural,
              singular: list.singular
            },
            fields: list.fields
          }}
          currentSort={sort}
        />

        {/* Status Summary Cards */}
        <div className="px-4 md:px-6 mb-6">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">All Discounts</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-50 mt-1">
                {statusCounts.all}
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-4 w-4 text-emerald-500" />
                <span className="text-sm font-medium">Active</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-50 mt-1">
                {statusCounts.active}
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">Disabled</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-50 mt-1">
                {statusCounts.disabled}
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">Expired</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-50 mt-1">
                {statusCounts.expired}
              </p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Circle className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Scheduled</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-50 mt-1">
                {statusCounts.scheduled}
              </p>
            </div>
          </div>
        </div>

        {/* Discounts List */}
        <div className="flex-1 px-4 md:px-6">
          {discounts.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-50">
                  No discounts found
                </h3>
                <p className="text-muted-foreground">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {discounts.map((discount) => (
                <DiscountDetailsComponent key={discount.id} discount={discount} />
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-4 md:px-6 py-4 border-t">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              totalItems={totalCount}
              itemsPerPage={pageSize}
            />
          </div>
        )}
      </div>
    </div>
  );
}
