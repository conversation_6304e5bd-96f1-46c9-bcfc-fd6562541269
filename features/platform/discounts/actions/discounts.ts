'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';

// Types for Discount data
export interface DiscountRule {
  id: string;
  type: 'fixed' | 'percentage' | 'free_shipping';
  value: number;
  allocation?: 'total' | 'item';
  description?: string;
}

export interface DiscountUsage {
  id: string;
  displayId: number;
  total: string;
  createdAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface DiscountWithRelations {
  id: string;
  code: string;
  isDynamic: boolean;
  isDisabled: boolean;
  stackable: boolean;
  startsAt: string;
  endsAt?: string;
  usageLimit?: number;
  usageCount: number;
  validDuration?: string;
  discountRule?: DiscountRule;
  orders: DiscountUsage[];
  regions: Array<{
    id: string;
    name: string;
    currencyCode: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

// GraphQL Queries
const GET_FILTERED_DISCOUNTS_QUERY = `
  query GetFilteredDiscounts(
    $where: DiscountWhereInput
    $orderBy: [DiscountOrderByInput!]
    $take: Int
    $skip: Int
  ) {
    discounts(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      code
      isDynamic
      isDisabled
      stackable
      startsAt
      endsAt
      usageLimit
      usageCount
      validDuration
      discountRule {
        id
        type
        value
        allocation
        description
      }
      orders(take: 3, orderBy: { createdAt: desc }) {
        id
        displayId
        total
        createdAt
        user {
          id
          name
          email
        }
      }
      regions {
        id
        name
        currency {
          code
        }
      }
      createdAt
      updatedAt
    }
    discountsCount(where: $where)
  }
`;

const GET_DISCOUNT_QUERY = `
  query GetDiscount($id: ID!) {
    discount(where: { id: $id }) {
      id
      code
      isDynamic
      isDisabled
      stackable
      startsAt
      endsAt
      usageLimit
      usageCount
      validDuration
      discountRule {
        id
        type
        value
        allocation
        description
      }
      orders(orderBy: { createdAt: desc }) {
        id
        displayId
        total
        createdAt
        user {
          id
          name
          email
        }
      }
      regions {
        id
        name
        currency {
          code
        }
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_DISCOUNT_STATUS_COUNTS_QUERY = `
  query GetDiscountStatusCounts {
    allDiscountsCount: discountsCount
    activeDiscountsCount: discountsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { startsAt: { lte: "${new Date().toISOString()}" } },
        { OR: [
          { endsAt: { gte: "${new Date().toISOString()}" } },
          { endsAt: { equals: null } }
        ]}
      ]
    })
    disabledDiscountsCount: discountsCount(where: { isDisabled: { equals: true } })
    expiredDiscountsCount: discountsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { endsAt: { lt: "${new Date().toISOString()}" } }
      ]
    })
    scheduledDiscountsCount: discountsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { startsAt: { gt: "${new Date().toISOString()}" } }
      ]
    })
  }
`;

// Server Actions
export async function getFilteredDiscounts({
  search = '',
  isDisabled,
  status,
  page = 1,
  pageSize = 20,
  sortBy = 'createdAt',
  sortOrder = 'desc'
}: {
  search?: string;
  isDisabled?: boolean;
  status?: 'active' | 'disabled' | 'expired' | 'scheduled';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  try {
    const skip = (page - 1) * pageSize;
    const now = new Date().toISOString();

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { code: { contains: search, mode: 'insensitive' } },
        { discountRule: { description: { contains: search, mode: 'insensitive' } } }
      ];
    }

    if (isDisabled !== undefined) {
      where.isDisabled = { equals: isDisabled };
    }

    // Status-based filtering
    if (status) {
      switch (status) {
        case 'active':
          where.AND = [
            { isDisabled: { equals: false } },
            { startsAt: { lte: now } },
            { OR: [
              { endsAt: { gte: now } },
              { endsAt: { equals: null } }
            ]}
          ];
          break;
        case 'disabled':
          where.isDisabled = { equals: true };
          break;
        case 'expired':
          where.AND = [
            { isDisabled: { equals: false } },
            { endsAt: { lt: now } }
          ];
          break;
        case 'scheduled':
          where.AND = [
            { isDisabled: { equals: false } },
            { startsAt: { gt: now } }
          ];
          break;
      }
    }

    const orderBy = [{ [sortBy]: sortOrder }];

    const response = await keystoneClient(GET_FILTERED_DISCOUNTS_QUERY, {
      where,
      orderBy,
      take: pageSize,
      skip
    });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: {
        discounts: response.data.discounts as DiscountWithRelations[],
        totalCount: response.data.discountsCount,
        page,
        pageSize,
        totalPages: Math.ceil(response.data.discountsCount / pageSize)
      }
    };
  } catch (error) {
    console.error('Error fetching discounts:', error);
    return {
      success: false,
      error: 'Failed to fetch discounts'
    };
  }
}

export async function getDiscount(id: string) {
  try {
    const response = await keystoneClient(GET_DISCOUNT_QUERY, { id });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: {
        discount: response.data.discount as DiscountWithRelations
      }
    };
  } catch (error) {
    console.error('Error fetching discount:', error);
    return {
      success: false,
      error: 'Failed to fetch discount'
    };
  }
}

export async function getDiscountStatusCounts() {
  try {
    const response = await keystoneClient(GET_DISCOUNT_STATUS_COUNTS_QUERY);

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: {
        all: response.data.allDiscountsCount,
        active: response.data.activeDiscountsCount,
        disabled: response.data.disabledDiscountsCount,
        expired: response.data.expiredDiscountsCount,
        scheduled: response.data.scheduledDiscountsCount
      }
    };
  } catch (error) {
    console.error('Error fetching discount status counts:', error);
    return {
      success: false,
      error: 'Failed to fetch discount status counts'
    };
  }
}

export async function updateDiscount(id: string, data: Partial<DiscountWithRelations>) {
  try {
    const UPDATE_DISCOUNT_QUERY = `
      mutation UpdateDiscount($id: ID!, $data: DiscountUpdateInput!) {
        updateDiscount(where: { id: $id }, data: $data) {
          id
          code
          isDisabled
          usageCount
          usageLimit
        }
      }
    `;

    const response = await keystoneClient(UPDATE_DISCOUNT_QUERY, { id, data });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true,
      data: response.data.updateDiscount
    };
  } catch (error) {
    console.error('Error updating discount:', error);
    return {
      success: false,
      error: 'Failed to update discount'
    };
  }
}

export async function deleteDiscount(id: string) {
  try {
    const DELETE_DISCOUNT_QUERY = `
      mutation DeleteDiscount($id: ID!) {
        deleteDiscount(where: { id: $id }) {
          id
        }
      }
    `;

    const response = await keystoneClient(DELETE_DISCOUNT_QUERY, { id });

    if (!response.success) {
      return {
        success: false,
        error: response.error
      };
    }

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting discount:', error);
    return {
      success: false,
      error: 'Failed to delete discount'
    };
  }
}
