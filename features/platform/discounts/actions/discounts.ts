'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';

// Types for Discount data
export interface DiscountRule {
  id: string;
  type: 'fixed' | 'percentage' | 'free_shipping';
  value: number;
  allocation?: 'total' | 'item';
  description?: string;
}

export interface DiscountUsage {
  id: string;
  displayId: number;
  total: string;
  createdAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface DiscountWithRelations {
  id: string;
  code: string;
  isDynamic: boolean;
  isDisabled: boolean;
  stackable: boolean;
  startsAt: string;
  endsAt?: string;
  usageLimit?: number;
  usageCount: number;
  validDuration?: string;
  discountRule?: DiscountRule;
  orders: DiscountUsage[];
  regions: Array<{
    id: string;
    name: string;
    currencyCode: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

// GraphQL Queries
const GET_FILTERED_DISCOUNTS = gql`
  query GetFilteredDiscounts(
    $where: DiscountWhereInput
    $orderBy: [DiscountOrderByInput!]
    $take: Int
    $skip: Int
  ) {
    discounts(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      code
      isDynamic
      isDisabled
      stackable
      startsAt
      endsAt
      usageLimit
      usageCount
      validDuration
      discountRule {
        id
        type
        value
        allocation
        description
      }
      orders(take: 3, orderBy: { createdAt: desc }) {
        id
        displayId
        total
        createdAt
        user {
          id
          name
          email
        }
      }
      regions {
        id
        name
        currencyCode
      }
      createdAt
      updatedAt
    }
    discountsCount(where: $where)
  }
`;

const GET_DISCOUNT = gql`
  query GetDiscount($id: ID!) {
    discount(where: { id: $id }) {
      id
      code
      isDynamic
      isDisabled
      stackable
      startsAt
      endsAt
      usageLimit
      usageCount
      validDuration
      discountRule {
        id
        type
        value
        allocation
        description
      }
      orders(orderBy: { createdAt: desc }) {
        id
        displayId
        total
        createdAt
        user {
          id
          name
          email
        }
      }
      regions {
        id
        name
        currencyCode
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_DISCOUNT_STATUS_COUNTS = gql`
  query GetDiscountStatusCounts {
    allDiscountsCount: discountsCount
    activeDiscountsCount: discountsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { startsAt: { lte: "${new Date().toISOString()}" } },
        { OR: [
          { endsAt: { gte: "${new Date().toISOString()}" } },
          { endsAt: { equals: null } }
        ]}
      ]
    })
    disabledDiscountsCount: discountsCount(where: { isDisabled: { equals: true } })
    expiredDiscountsCount: discountsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { endsAt: { lt: "${new Date().toISOString()}" } }
      ]
    })
    scheduledDiscountsCount: discountsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { startsAt: { gt: "${new Date().toISOString()}" } }
      ]
    })
  }
`;

// Server Actions
export async function getFilteredDiscounts({
  search = '',
  isDisabled,
  status,
  page = 1,
  pageSize = 20,
  sortBy = 'createdAt',
  sortOrder = 'desc'
}: {
  search?: string;
  isDisabled?: boolean;
  status?: 'active' | 'disabled' | 'expired' | 'scheduled';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  try {
    const skip = (page - 1) * pageSize;
    const now = new Date().toISOString();

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { code: { contains: search, mode: 'insensitive' } },
        { discountRule: { description: { contains: search, mode: 'insensitive' } } }
      ];
    }

    if (isDisabled !== undefined) {
      where.isDisabled = { equals: isDisabled };
    }

    // Status-based filtering
    if (status) {
      switch (status) {
        case 'active':
          where.AND = [
            { isDisabled: { equals: false } },
            { startsAt: { lte: now } },
            { OR: [
              { endsAt: { gte: now } },
              { endsAt: { equals: null } }
            ]}
          ];
          break;
        case 'disabled':
          where.isDisabled = { equals: true };
          break;
        case 'expired':
          where.AND = [
            { isDisabled: { equals: false } },
            { endsAt: { lt: now } }
          ];
          break;
        case 'scheduled':
          where.AND = [
            { isDisabled: { equals: false } },
            { startsAt: { gt: now } }
          ];
          break;
      }
    }

    const orderBy = [{ [sortBy]: sortOrder }];

    const data = await graphqlClient.request(GET_FILTERED_DISCOUNTS, {
      where,
      orderBy,
      take: pageSize,
      skip
    });

    return {
      success: true,
      data: {
        discounts: data.discounts as DiscountWithRelations[],
        totalCount: data.discountsCount,
        page,
        pageSize,
        totalPages: Math.ceil(data.discountsCount / pageSize)
      }
    };
  } catch (error) {
    console.error('Error fetching discounts:', error);
    return {
      success: false,
      error: 'Failed to fetch discounts'
    };
  }
}

export async function getDiscount(id: string) {
  try {
    const data = await graphqlClient.request(GET_DISCOUNT, { id });

    return {
      success: true,
      data: {
        discount: data.discount as DiscountWithRelations
      }
    };
  } catch (error) {
    console.error('Error fetching discount:', error);
    return {
      success: false,
      error: 'Failed to fetch discount'
    };
  }
}

export async function getDiscountStatusCounts() {
  try {
    const data = await graphqlClient.request(GET_DISCOUNT_STATUS_COUNTS);

    return {
      success: true,
      data: {
        all: data.allDiscountsCount,
        active: data.activeDiscountsCount,
        disabled: data.disabledDiscountsCount,
        expired: data.expiredDiscountsCount,
        scheduled: data.scheduledDiscountsCount
      }
    };
  } catch (error) {
    console.error('Error fetching discount status counts:', error);
    return {
      success: false,
      error: 'Failed to fetch discount status counts'
    };
  }
}

export async function updateDiscount(id: string, data: Partial<DiscountWithRelations>) {
  try {
    const UPDATE_DISCOUNT = gql`
      mutation UpdateDiscount($id: ID!, $data: DiscountUpdateInput!) {
        updateDiscount(where: { id: $id }, data: $data) {
          id
          code
          isDisabled
          usageCount
          usageLimit
        }
      }
    `;

    const result = await graphqlClient.request(UPDATE_DISCOUNT, { id, data });

    return {
      success: true,
      data: result.updateDiscount
    };
  } catch (error) {
    console.error('Error updating discount:', error);
    return {
      success: false,
      error: 'Failed to update discount'
    };
  }
}

export async function deleteDiscount(id: string) {
  try {
    const DELETE_DISCOUNT = gql`
      mutation DeleteDiscount($id: ID!) {
        deleteDiscount(where: { id: $id }) {
          id
        }
      }
    `;

    await graphqlClient.request(DELETE_DISCOUNT, { id });

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting discount:', error);
    return {
      success: false,
      error: 'Failed to delete discount'
    };
  }
}
