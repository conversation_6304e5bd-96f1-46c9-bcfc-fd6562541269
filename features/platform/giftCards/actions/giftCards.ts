'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';

// Types for Gift Card data
export interface GiftCardTransaction {
  id: string;
  amount: number;
  createdAt: string;
  order?: {
    id: string;
    displayId: number;
  };
}

export interface GiftCardWithRelations {
  id: string;
  code: string;
  value: number;
  balance: number;
  isDisabled: boolean;
  endsAt?: string;
  order?: {
    id: string;
    displayId: number;
    user?: {
      id: string;
      name: string;
      email: string;
    };
  };
  region?: {
    id: string;
    name: string;
    currencyCode: string;
  };
  giftCardTransactions: GiftCardTransaction[];
  createdAt: string;
  updatedAt: string;
}

// GraphQL Queries
const GET_FILTERED_GIFT_CARDS = gql`
  query GetFilteredGiftCards(
    $where: GiftCardWhereInput
    $orderBy: [GiftCardOrderByInput!]
    $take: Int
    $skip: Int
  ) {
    giftCards(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      code
      value
      balance
      isDisabled
      endsAt
      order {
        id
        displayId
        user {
          id
          name
          email
        }
      }
      region {
        id
        name
        currencyCode
      }
      giftCardTransactions(take: 3, orderBy: { createdAt: desc }) {
        id
        amount
        createdAt
        order {
          id
          displayId
        }
      }
      createdAt
      updatedAt
    }
    giftCardsCount(where: $where)
  }
`;

const GET_GIFT_CARD = gql`
  query GetGiftCard($id: ID!) {
    giftCard(where: { id: $id }) {
      id
      code
      value
      balance
      isDisabled
      endsAt
      order {
        id
        displayId
        user {
          id
          name
          email
        }
      }
      region {
        id
        name
        currencyCode
      }
      giftCardTransactions(orderBy: { createdAt: desc }) {
        id
        amount
        createdAt
        order {
          id
          displayId
        }
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_GIFT_CARD_STATUS_COUNTS = gql`
  query GetGiftCardStatusCounts {
    allGiftCardsCount: giftCardsCount
    activeGiftCardsCount: giftCardsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { balance: { gt: 0 } },
        { OR: [
          { endsAt: { gte: "${new Date().toISOString()}" } },
          { endsAt: { equals: null } }
        ]}
      ]
    })
    disabledGiftCardsCount: giftCardsCount(where: { isDisabled: { equals: true } })
    expiredGiftCardsCount: giftCardsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { endsAt: { lt: "${new Date().toISOString()}" } }
      ]
    })
    depletedGiftCardsCount: giftCardsCount(where: {
      AND: [
        { isDisabled: { equals: false } },
        { balance: { equals: 0 } }
      ]
    })
  }
`;

// Server Actions
export async function getFilteredGiftCards({
  search = '',
  isDisabled,
  status,
  page = 1,
  pageSize = 20,
  sortBy = 'createdAt',
  sortOrder = 'desc'
}: {
  search?: string;
  isDisabled?: boolean;
  status?: 'active' | 'disabled' | 'expired' | 'depleted';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  try {
    const skip = (page - 1) * pageSize;
    const now = new Date().toISOString();

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { code: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (isDisabled !== undefined) {
      where.isDisabled = { equals: isDisabled };
    }

    // Status-based filtering
    if (status) {
      switch (status) {
        case 'active':
          where.AND = [
            { isDisabled: { equals: false } },
            { balance: { gt: 0 } },
            { OR: [
              { endsAt: { gte: now } },
              { endsAt: { equals: null } }
            ]}
          ];
          break;
        case 'disabled':
          where.isDisabled = { equals: true };
          break;
        case 'expired':
          where.AND = [
            { isDisabled: { equals: false } },
            { endsAt: { lt: now } }
          ];
          break;
        case 'depleted':
          where.AND = [
            { isDisabled: { equals: false } },
            { balance: { equals: 0 } }
          ];
          break;
      }
    }

    const orderBy = [{ [sortBy]: sortOrder }];

    const data = await graphqlClient.request(GET_FILTERED_GIFT_CARDS, {
      where,
      orderBy,
      take: pageSize,
      skip
    });

    return {
      success: true,
      data: {
        giftCards: data.giftCards as GiftCardWithRelations[],
        totalCount: data.giftCardsCount,
        page,
        pageSize,
        totalPages: Math.ceil(data.giftCardsCount / pageSize)
      }
    };
  } catch (error) {
    console.error('Error fetching gift cards:', error);
    return {
      success: false,
      error: 'Failed to fetch gift cards'
    };
  }
}

export async function getGiftCard(id: string) {
  try {
    const data = await graphqlClient.request(GET_GIFT_CARD, { id });

    return {
      success: true,
      data: {
        giftCard: data.giftCard as GiftCardWithRelations
      }
    };
  } catch (error) {
    console.error('Error fetching gift card:', error);
    return {
      success: false,
      error: 'Failed to fetch gift card'
    };
  }
}

export async function getGiftCardStatusCounts() {
  try {
    const data = await graphqlClient.request(GET_GIFT_CARD_STATUS_COUNTS);

    return {
      success: true,
      data: {
        all: data.allGiftCardsCount,
        active: data.activeGiftCardsCount,
        disabled: data.disabledGiftCardsCount,
        expired: data.expiredGiftCardsCount,
        depleted: data.depletedGiftCardsCount
      }
    };
  } catch (error) {
    console.error('Error fetching gift card status counts:', error);
    return {
      success: false,
      error: 'Failed to fetch gift card status counts'
    };
  }
}

export async function updateGiftCard(id: string, data: Partial<GiftCardWithRelations>) {
  try {
    const UPDATE_GIFT_CARD = gql`
      mutation UpdateGiftCard($id: ID!, $data: GiftCardUpdateInput!) {
        updateGiftCard(where: { id: $id }, data: $data) {
          id
          code
          value
          balance
          isDisabled
        }
      }
    `;

    const result = await graphqlClient.request(UPDATE_GIFT_CARD, { id, data });

    return {
      success: true,
      data: result.updateGiftCard
    };
  } catch (error) {
    console.error('Error updating gift card:', error);
    return {
      success: false,
      error: 'Failed to update gift card'
    };
  }
}

export async function deleteGiftCard(id: string) {
  try {
    const DELETE_GIFT_CARD = gql`
      mutation DeleteGiftCard($id: ID!) {
        deleteGiftCard(where: { id: $id }) {
          id
        }
      }
    `;

    await graphqlClient.request(DELETE_GIFT_CARD, { id });

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting gift card:', error);
    return {
      success: false,
      error: 'Failed to delete gift card'
    };
  }
}
