import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { MoreHorizontal, Calendar, CreditCard, DollarSign, User } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import type { GiftCard } from "../lib/types";

interface GiftCardDetailsComponentProps {
  giftCard: GiftCard;
}

export function GiftCardDetailsComponent({ giftCard }: GiftCardDetailsComponentProps) {
  const getGiftCardStatus = () => {
    const now = new Date();
    const endsAt = giftCard.endsAt ? new Date(giftCard.endsAt) : null;

    if (giftCard.isDisabled) return { status: 'disabled', color: 'gray' };
    if (giftCard.balance === 0) return { status: 'depleted', color: 'red' };
    if (endsAt && endsAt < now) return { status: 'expired', color: 'red' };
    return { status: 'active', color: 'emerald' };
  };

  const { status, color } = getGiftCardStatus();

  const getBalancePercentage = () => {
    if (giftCard.value === 0) return 0;
    return (giftCard.balance / giftCard.value) * 100;
  };

  const formatCurrency = (amount: number) => {
    const currencyCode = giftCard.region?.currencyCode || 'USD';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode.toUpperCase(),
    }).format(amount / 100);
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <Link 
                  href={`/platform/gift-cards/${giftCard.id}`}
                  className="text-lg font-semibold text-gray-900 dark:text-gray-50 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  {giftCard.code}
                </Link>
                <Badge variant="outline" className={`bg-${color}-50 text-${color}-700 border-${color}-200`}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Badge>
              </div>
              <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <DollarSign className="h-4 w-4" />
                  <span>{formatCurrency(giftCard.balance)} / {formatCurrency(giftCard.value)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CreditCard className="h-4 w-4" />
                  <span>{giftCard.giftCardTransactions.length} transactions</span>
                </div>
                {giftCard.endsAt && (
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Expires {new Date(giftCard.endsAt).toLocaleDateString()}</span>
                  </div>
                )}
                {giftCard.region && (
                  <Badge variant="outline" size="sm">
                    {giftCard.region.name}
                  </Badge>
                )}
              </div>
              {giftCard.order && (
                <div className="mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4" />
                    <span>Purchased in order </span>
                    <Link 
                      href={`/platform/orders/${giftCard.order.id}`}
                      className="font-medium text-blue-600 hover:text-blue-800"
                    >
                      #{giftCard.order.displayId}
                    </Link>
                    {giftCard.order.user && (
                      <span>by {giftCard.order.user.name}</span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/platform/gift-cards/${giftCard.id}`}>
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/platform/gift-cards/${giftCard.id}/edit`}>
                  Edit Gift Card
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                {giftCard.isDisabled ? 'Enable' : 'Disable'}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete Gift Card
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      {/* Balance Progress Bar */}
      <CardContent className="pt-0">
        <div className="border-t pt-3">
          <div className="flex items-center justify-between text-sm mb-2">
            <span className="text-muted-foreground">Balance</span>
            <span className="font-medium">
              {formatCurrency(giftCard.balance)} remaining
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`bg-${color}-500 h-2 rounded-full transition-all duration-300`}
              style={{ width: `${getBalancePercentage()}%` }}
            />
          </div>
        </div>
      </CardContent>

      {/* Recent Transactions */}
      {giftCard.giftCardTransactions.length > 0 && (
        <CardContent className="pt-0">
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-50 mb-2">
              Recent Transactions
            </h4>
            <div className="space-y-2">
              {giftCard.giftCardTransactions.slice(0, 3).map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <span className={`font-medium ${transaction.amount < 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {transaction.amount < 0 ? '-' : '+'}{formatCurrency(Math.abs(transaction.amount))}
                    </span>
                    {transaction.order && (
                      <Link 
                        href={`/platform/orders/${transaction.order.id}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Order #{transaction.order.displayId}
                      </Link>
                    )}
                  </div>
                  <span className="text-muted-foreground">
                    {new Date(transaction.createdAt).toLocaleDateString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
