// Gift Card-related types for the platform

export interface GiftCardTransaction {
  id: string;
  amount: number;
  createdAt: string;
  order?: {
    id: string;
    displayId: number;
  };
}

export interface GiftCardOrder {
  id: string;
  displayId: number;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface GiftCardRegion {
  id: string;
  name: string;
  currencyCode: string;
}

export interface GiftCard {
  id: string;
  code: string;
  value: number;
  balance: number;
  isDisabled: boolean;
  endsAt?: string;
  order?: GiftCardOrder;
  region?: GiftCardRegion;
  giftCardTransactions: GiftCardTransaction[];
  createdAt: string;
  updatedAt: string;
}

export interface GiftCardListResponse {
  giftCards: GiftCard[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface GiftCardStatusCounts {
  all: number;
  active: number;
  disabled: number;
  expired: number;
  depleted: number;
}

export interface GiftCardFilters {
  search?: string;
  isDisabled?: boolean;
  status?: 'active' | 'disabled' | 'expired' | 'depleted';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export type GiftCardStatus = 'active' | 'disabled' | 'expired' | 'depleted';
